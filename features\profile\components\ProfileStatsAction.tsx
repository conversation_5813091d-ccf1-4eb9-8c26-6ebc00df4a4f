import { MyUserProfileType } from "@/hooks/useAuth";
import globalStyles from "@/lib/globalStyles";
import React from "react";
import { StyleSheet, Text, View } from "react-native";
import Gap from "@/components/Gap";
import { Feather } from "@expo/vector-icons";
import ButtonCircle from "@/components/ButtonCircle";
import { router } from "expo-router";
import { useTranslation } from "react-i18next";

type Props = {
  onLogout: () => void;
  onDelete: () => void;
  user?: MyUserProfileType | null;
};

const styles = StyleSheet.create({
  buttonOutline: {
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
    backgroundColor: "transparent",
  },
});

const ProfileStatsAction = ({ onLogout, onDelete, user }: Props) => {
  const { t } = useTranslation();

  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      <View>
        <Text
          style={{
            fontSize: globalStyles.size["2xl"],
            fontWeight: "600",
            color: globalStyles.colors.light.secondary,
          }}
        >
          {t("event.favorite_events")}
        </Text>
        {/* <Text
          style={{
            fontSize: globalStyles.size["3xl"],
            fontWeight: "bold",
            color: globalStyles.colors.tertiary2,
          }}
        >
          {user?.favoriteEvents?.length || 0} Eventos
        </Text> */}
      </View>
      <View
        style={{
          flexDirection: "row",
        }}
      >
        <ButtonCircle
          style={{
            backgroundColor: globalStyles.colors.primary1,
          }}
          onPress={() => router.push("/profile-edit")}
        >
          <Feather name="edit" size={14} color={globalStyles.colors.white} />
        </ButtonCircle>
        <Gap x={globalStyles.gap["2xs"]} />
        <ButtonCircle
          style={styles.buttonOutline}
          onPress={() => router.push("/settings")}
        >
          <Feather
            name="settings"
            size={14}
            color={globalStyles.rgba().primary1}
          />
        </ButtonCircle>
        <Gap x={globalStyles.gap["2xs"]} />
        <ButtonCircle onPress={onLogout} style={styles.buttonOutline}>
          <Feather
            name="log-out"
            size={14}
            color={globalStyles.rgba().primary1}
          />
        </ButtonCircle>
        <Gap x={globalStyles.gap["2xs"]} />
        <ButtonCircle onPress={onDelete} style={styles.buttonOutline}>
          <Feather
            name="trash"
            size={14}
            color={globalStyles.rgba().primary1}
          />
        </ButtonCircle>
      </View>
    </View>
  );
};

export default ProfileStatsAction;
